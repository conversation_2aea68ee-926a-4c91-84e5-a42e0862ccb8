const express = require('express');
const router = express.Router();
const Stripe = require('stripe');
const rateLimit = require('express-rate-limit');
const allowPaymentAction = require('../middleware/workspaceAuthMiddleware');
const { authenticateToken } = require('../middleware/authMiddleware');
const { validatePriceId, calculateAmount, calculateProratedAmount } = require('../utils/helper');
const Subscription = require('../models/Subscription');
const Workspace = require('../models/Workspace');
const User = require('../models/User');

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Rate limiting for payment attempts
const paymentLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 10
});

// Rate limiting for webhooks
const webhookLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100
});

router.post('/create-payment-intent',
    paymentLimiter,
    authenticateToken,
    allowPaymentAction,
    async (req, res) => {
        const {
            priceId,
            workspaceId,
            userCount,
            billingCycle,
            planType
        } = req.body;

        try {
            // Validate inputs
            if (!priceId || !workspaceId || !userCount || !billingCycle || !planType) {
                return res.status(400).json({ error: 'Missing required parameters' });
            }

            // Validate price ID
            if (!validatePriceId(priceId)) {
                return res.status(400).json({ error: 'Invalid price ID' });
            }

            // Create or update customer in Stripe
            let customer;
            const workspace = await Workspace.findById(workspaceId)
                .populate('subscription');

            if (workspace.subscription?.stripeCustomerId) {
                customer = await stripe.customers.retrieve(workspace.subscription.stripeCustomerId);
            } else {
                const user = await User.findById(req.user.id);
                customer = await stripe.customers.create({
                    email: user.email,
                    metadata: {
                        workspaceId,
                        userId: req.user.id
                    }
                });
            }

            // Calculate total amount (in cents)
            const amount = calculateAmount(planType, billingCycle, userCount);

            // Create PaymentIntent first
            const paymentIntent = await stripe.paymentIntents.create({
                amount,
                currency: 'usd',
                customer: customer.id,
                metadata: {
                    workspaceId,
                    userId: req.user.id,
                    planType,
                    billingCycle,
                    userCount,
                    priceId
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            // Create or update subscription in our database
            const subscriptionDoc = await Subscription.findOneAndUpdate(
                { workspaceId },
                {
                    workspaceId,
                    status: 'incomplete',
                    planType,
                    billingCycle,
                    priceId,
                    quantity: userCount,
                    stripeCustomerId: customer.id,
                    currentPeriodStart: new Date(),
                    currentPeriodEnd: new Date(Date.now() + (billingCycle === 'annually' ? 365 : 30) * 24 * 60 * 60 * 1000),
                    cancelAtPeriodEnd: false
                },
                { upsert: true, new: true }
            );

            // Update workspace with subscription reference
            await Workspace.findByIdAndUpdate(workspaceId, {
                subscription: subscriptionDoc._id
            });

            // Return client secret
            res.json({
                clientSecret: paymentIntent.client_secret
            });

        } catch (err) {
            console.error('Payment intent creation error:', err);
            res.status(500).json({ error: 'Failed to create payment intent' });
        }
    }
);

// Get billing status for a workspace
router.get('/billing-status/:workspaceId',
    authenticateToken,
    allowPaymentAction,
    async (req, res) => {
        const { workspaceId } = req.params;

        try {
            // Get workspace with subscription
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ error: 'Workspace not found' });
            }

            const subscription = workspace.subscription;
            if (!subscription) {
                return res.status(200).json({
                    status: 'free',
                    planType: 'Free',
                    isPermanentlyPro: false,
                    reservedSeats: 0,
                    pendingSeats: 0,
                    removedSeats: 0,
                    currentMemberCount: workspace.members.length + (workspace.guests?.length || 0),
                    availableSeats: 0,
                    nextBillingDate: null
                });
            }

            // Calculate available seats (reserved seats minus current members plus removed seats)
            const currentMemberCount = workspace.members.length + (workspace.guests?.length || 0);
            const reservedSeats = subscription.reservedSeats || 0;
            const pendingSeats = subscription.pendingSeats || 0;
            const removedSeats = subscription.removedSeats || 0;

            // Available seats are either:
            // 1. Remaining reserved seats (if any)
            // 2. Removed seats that can be reused
            const remainingReservedSeats = Math.max(0, reservedSeats - currentMemberCount);
            const availableSeats = remainingReservedSeats + removedSeats;

            res.status(200).json({
                status: subscription.status,
                planType: subscription.planType,
                billingCycle: subscription.billingCycle,
                isPermanentlyPro: subscription.isPermanentlyPro || false,
                reservedSeats,
                pendingSeats,
                removedSeats,
                currentMemberCount,
                availableSeats,
                nextBillingDate: subscription.currentPeriodEnd,
                priceId: subscription.priceId
            });
        } catch (err) {
            console.error('Error getting billing status:', err);
            return res.status(500).json({ error: 'Failed to get billing status' });
        }
    }
);

// Add seats to an existing subscription with prorated billing
router.post('/add-seats',
    paymentLimiter,
    authenticateToken,
    allowPaymentAction,
    async (req, res) => {
        const {
            workspaceId,
            seatsToAdd,
            priceId,
            planType,
            billingCycle
        } = req.body;

        try {
            // Validate inputs
            if (!workspaceId || !seatsToAdd || seatsToAdd < 1 || !priceId || !planType || !billingCycle) {
                return res.status(400).json({ error: 'Missing required parameters' });
            }

            // Validate price ID
            if (!validatePriceId(priceId)) {
                return res.status(400).json({ error: 'Invalid price ID' });
            }

            // Get subscription
            const subscription = await Subscription.findOne({ workspaceId });
            if (!subscription) {
                return res.status(404).json({ error: 'Subscription not found' });
            }

            // Calculate prorated amount
            const proratedAmount = calculateProratedAmount(
                planType,
                billingCycle,
                seatsToAdd,
                subscription.currentPeriodStart,
                subscription.currentPeriodEnd
            );

            // Create or update customer in Stripe
            let customer;
            if (subscription.stripeCustomerId) {
                customer = await stripe.customers.retrieve(subscription.stripeCustomerId);
            } else {
                const user = await User.findById(req.user.id);
                customer = await stripe.customers.create({
                    email: user.email,
                    metadata: {
                        workspaceId,
                        userId: req.user.id
                    }
                });
            }

            // Create PaymentIntent for prorated amount
            const paymentIntent = await stripe.paymentIntents.create({
                amount: proratedAmount,
                currency: 'usd',
                customer: customer.id,
                metadata: {
                    workspaceId,
                    userId: req.user.id,
                    planType,
                    billingCycle,
                    userCount: seatsToAdd,
                    priceId,
                    isProrated: 'true',
                    isAdditionalSeats: 'true'
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            // Update subscription with pending seats
            subscription.pendingSeats = (subscription.pendingSeats || 0) + seatsToAdd;
            await subscription.save();

            // Return client secret
            res.json({
                clientSecret: paymentIntent.client_secret,
                proratedAmount: proratedAmount / 100, // Convert cents to dollars for frontend
                seatsToAdd,
                currentPeriodEnd: subscription.currentPeriodEnd
            });
        } catch (err) {
            console.error('Add seats error:', err);
            return res.status(500).json({ error: 'Failed to add seats' });
        }
    }
);

router.post('/webhook',
    webhookLimiter,
    express.raw({ type: 'application/json' }),

    async (req, res) => {

        const sig = req.headers['stripe-signature'];

        try {
            const event = stripe.webhooks.constructEvent(
                req.body,
                sig,
                process.env.STRIPE_WEBHOOK_SECRET
            );

            switch (event.type) {
                case 'payment_intent.succeeded':
                    await handlePaymentSuccess(event.data.object);
                    break;
                case 'payment_intent.payment_failed':
                    await handlePaymentFailure(event.data.object);
                    break;
                case 'customer.subscription.updated':
                    await handleSubscriptionUpdated(event.data.object);
                    break;
                case 'customer.subscription.deleted':
                    await handleSubscriptionDeletion(event.data.object);
                    break;
            }

            res.json({ received: true });
        } catch (err) {
            console.error('Webhook error:', err);
            return res.status(400).json({ error: 'Webhook signature verification failed' });
        }
    }
);

async function handlePaymentSuccess(paymentIntent) {
    const {
        workspaceId,
        userId,
        planType,
        billingCycle,
        priceId,
        userCount,
        isProrated,
        isAdditionalSeats
    } = paymentIntent.metadata;

    try {
        // Create or update subscription
        let subscription = await Subscription.findOne({ workspaceId });

        if (!subscription) {
            // New subscription
            subscription = new Subscription({
                workspaceId,
                status: 'active',
                planType,
                billingCycle,
                priceId,
                quantity: userCount,
                reservedSeats: userCount,
                isPermanentlyPro: true, // Mark as permanently pro
                stripeCustomerId: paymentIntent.customer,
                lastPaymentDate: new Date(),
                currentPeriodStart: new Date(),
                currentPeriodEnd: new Date(Date.now() + (billingCycle === 'annually' ? 365 : 30) * 24 * 60 * 60 * 1000)
            });
        } else if (isAdditionalSeats === 'true') {
            // Adding seats to existing subscription
            subscription.status = 'active';
            subscription.isPermanentlyPro = true; // Ensure it's marked as pro

            // Move pending seats to reserved seats
            const additionalSeats = parseInt(userCount);
            subscription.reservedSeats = (subscription.reservedSeats || 0) + additionalSeats;
            subscription.pendingSeats = Math.max(0, (subscription.pendingSeats || 0) - additionalSeats);

            subscription.lastPaymentDate = new Date();

            // Don't update period dates for additional seats - keep the existing billing cycle
        } else {
            // Regular subscription renewal or initial subscription
            subscription.status = 'active';
            subscription.planType = planType;
            subscription.billingCycle = billingCycle;
            subscription.priceId = priceId;
            subscription.quantity = userCount;
            subscription.reservedSeats = (subscription.reservedSeats || 0) + parseInt(userCount);
            subscription.isPermanentlyPro = true; // Mark as permanently pro
            subscription.lastPaymentDate = new Date();
            subscription.currentPeriodStart = new Date();
            subscription.currentPeriodEnd = new Date(Date.now() + (billingCycle === 'annually' ? 365 : 30) * 24 * 60 * 60 * 1000);
        }

        await subscription.save();

        // Update workspace with subscription reference
        await Workspace.findByIdAndUpdate(workspaceId, {
            subscription: subscription._id
        });

        // Get workspace and update members' premium status
        const workspace = await Workspace.findById(workspaceId).populate('members.user');

        if (workspace) {
            // Update all workspace members' premium status to true since payment was successful
            const memberUpdates = [...workspace.members.map(member => member.user), ...workspace.guests].map(async (user) => {
                if (user) {
                    await User.findByIdAndUpdate(user._id, {
                        isPremiumMember: true,
                        canSeeOnlineStatus: true
                    });
                }
            });

            await Promise.all(memberUpdates);
        }

    } catch (error) {
        console.error('Error handling payment success:', error);
        throw error;
    }
}

async function handlePaymentFailure(paymentIntent) {
    const { workspaceId } = paymentIntent.metadata;

    try {
        await Subscription.findOneAndUpdate(
            { workspaceId },
            {
                status: 'payment_failed',
                updatedAt: new Date()
            }
        );
    } catch (error) {
        console.error('Error handling payment failure:', error);
        throw error;
    }
}

async function handleSubscriptionUpdated(subscription) {
    const { workspaceId } = subscription.metadata;

    try {
        // Update subscription status
        await Subscription.findOneAndUpdate(
            { workspaceId },
            {
                status: subscription.status,
                updatedAt: new Date(),
                currentPeriodEnd: new Date(subscription.current_period_end * 1000),
                currentPeriodStart: new Date(subscription.current_period_start * 1000)
            }
        );

        // Get workspace and update members' premium status
        const workspace = await Workspace.findOne({ shortId: workspaceId }).populate('members.user');

        if (workspace) {
            const isPremium = subscription.status === 'active';

            // Update all workspace members' premium status
            if (workspace) {
                // Update all workspace members' premium status to true since payment was successful
                const memberUpdates = [...workspace.members.map(member => member.user), ...workspace.guests].map(async (user) => {
                    if (user) {
                        await User.findByIdAndUpdate(user._id, {
                            isPremiumMember: true,
                            canSeeOnlineStatus: true
                        });
                    }
                });

                await Promise.all(memberUpdates);
            }
        }
    } catch (error) {
        console.error('Error handling subscription update:', error);
        throw error;
    }
}

async function handleSubscriptionDeletion(subscription) {
    const { workspaceId } = subscription.metadata;

    try {
        // Update subscription status
        await Subscription.findOneAndUpdate(
            { workspaceId },
            {
                status: 'canceled',
                canceledAt: new Date(),
                updatedAt: new Date()
            }
        );

        // Get workspace and update members' premium status
        const workspace = await Workspace.findOne({ shortId: workspaceId }).populate('members.user');

        if (workspace) {
            // Update all workspace members' and guests' premium status to false
            const userUpdates = [...workspace.members.map(member => member.user), ...workspace.guests].map(async (user) => {
                if (user) {
                    await User.findByIdAndUpdate(user._id, {
                        isPremiumMember: false,
                        canSeeOnlineStatus: false
                    });
                }
            });

            await Promise.all(userUpdates);
        }
    } catch (error) {
        console.error('Error handling subscription deletion:', error);
        throw error;
    }
}

module.exports = router;