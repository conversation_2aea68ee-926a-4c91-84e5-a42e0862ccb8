const Workspace = require("../models/Workspace");

const allowPaymentAction = async (req, res, next) => {
    // Get workspaceId from either body or params
    const workspaceId = req.body.workspaceId || req.params.workspaceId;
    const userId = req.user.id;

    if (!workspaceId) {
        return res.status(400).json({
            error: 'Workspace ID is required'
        });
    }

    try {
        // First try to find by _id
        let workspace = await Workspace.findOne({
            _id: workspaceId,
            $or: [
                { ownerId: userId },
                { 'members.user': userId, 'members.role': 'admin' }
            ]
        });

        // If not found, try by shortId
        if (!workspace) {
            workspace = await Workspace.findOne({
                shortId: workspaceId,
                $or: [
                    { ownerId: userId },
                    { 'members.user': userId, 'members.role': 'admin' }
                ]
            });
        }

        if (!workspace) {
            return res.status(403).json({
                error: 'Only workspace owners and admins can perform this action'
            });
        }

        req.workspace = workspace;
        next();
    } catch (err) {
        console.error('Workspace authorization error:', err);
        return res.status(500).json({
            error: 'Internal server error'
        });
    }
};

module.exports = allowPaymentAction;