const Card = require('../models/Card');
const Notification = require('../models/Notification');
const Activity = require('../models/Activity');
const UsedToken = require('../models/UsedToken');
const Board = require('../models/Board');

const crypto = require('crypto');
const jwt = require('jsonwebtoken');

const { OAuth2Client } = require('google-auth-library');
const User = require('../models/User');
const { sendPushNotification } = require('./firebaseAdmin');



const notifyWatchers = async ({
    cardId,
    initiatorId,
    actionType,
    targetType = 'Card',
    targetId = cardId,
    message,
    io
}) => {
    const card = await Card.findById(cardId).populate('watchers');

    // Filter out the initiator (the user who performed the action) from the watchers
    const filteredWatchers = card.watchers.filter(watcher => !watcher._id.equals(initiatorId));

    // Create notifications for the filtered watchers
    const notifications = await Notification.insertMany(
        filteredWatchers.map(watcher => ({
            member: watcher._id,
            initiator: initiatorId,
            type: actionType,
            targetType: targetType,
            targetId: targetId,
            message: message,
        }))
    );

    // Populate the initiator data
    const populatedNotifications = await Notification.populate(notifications, {
        path: 'initiator',
        select: 'name username email profilePicture online'
    });

    // Emit the notification to each watcher
    populatedNotifications.forEach(notification => {
        io.to(notification.member.toString()).emit('newNotification', notification);
    });
};

const notifyMentionedMembers = async ({
    initiatorId,
    mentionedIds = [],
    targetType = 'Card',
    targetId,
    message,
    io
}) => {
    // Create notifications for the mentioned members
    const notifications = await Notification.insertMany(
        mentionedIds.map(memberId => ({
            member: memberId,
            initiator: initiatorId,
            type: 'mention',
            targetType: targetType,
            targetId: targetId,
            message: message,
        }))
    );

    // Populate the initiator data
    const populatedNotifications = await Notification.populate(notifications, {
        path: 'initiator',
        select: 'name username email profilePicture online'
    });

    // Emit the notification to each mentioned member
    populatedNotifications.forEach(notification => {
        io.to(notification.member.toString()).emit('newNotification', notification);
    });
};

// Centralized function to emit events
// CommonJS syntax
const emitUserAction = (io, userId, actionType, data) => {
    io.to(userId).emit('user-action', {
        action: actionType,
        ...data
    });

    return null
};

const emitIOCardAction = async (req, boardId, actionType, payload) => {
    const io = req.app.get('socketio');

    const board = await Board.findOne({ shortId: boardId }).populate({
        path: 'members.user',
        select: '_id username',
    });

    if (!board) {
        console.log('Board not found');
        return;
    }

    board.members.forEach(member => {
        emitUserAction(io, member?.user?._id?.toString(), actionType, payload);
    });
};


const convertTrelloExportToZoobbe = (data, workspaceId) => {
    const result = [];

    // Add board information
    result.push({
        workspaceId: workspaceId,
        title: data.name,
        boardId: data.id,
        import_type: "board",
        archived: data.closed || false  // Trello uses 'closed' for archived
    });

    // Add action lists
    data.lists?.forEach(list => {
        result.push({
            actionListId: list.id,
            boardId: list.idBoard,
            title: list.name,
            archived: list.closed || false,  // Action list archived
            import_type: "actionlist"
        });

        // Add cards under each action list
        data.cards?.filter(card => card.idList === list.id).forEach((card, index) => {
            result.push({
                actionListId: list.id,
                boardId: card.idBoard,
                title: card.name || "Untitled Card",
                description: card.desc || "",
                users: card.idMembers || [],
                archived: card.closed || false,  // Card archived
                order: index, //
                cardNumber: index + 1, //
                dueDate: {
                    date: card.due || null,
                    startDate: card.start || null,
                    dueTime: card.dueReminder || '',
                    reminder: card.dueComplete ? 'Completed' : 'None',
                    completed: card.dueComplete || false,
                    status: card.dueComplete ? 'Completed' : 'Pending'
                },
                comments: data.actions?.filter(action => action.data.card && action.data.card.id === card.id && action.type === "commentCard").map(comment => ({
                    text: comment.data.text,
                    createdAt: comment.date,
                    user: comment.idMemberCreator
                })) || [],
                checklists: data.checklists?.filter(checklist => checklist.idCard === card.id).map(checklist => ({
                    _id: checklist.id,
                    title: checklist.name,
                    items: checklist.checkItems.map(item => ({
                        _id: item.id,
                        title: item.name,
                        checked: item.state === "complete",
                        createdAt: item.dateLastActivity || new Date().toISOString()
                    }))
                })) || [],
                attachments: card.attachments?.map(attachment => ({
                    _id: attachment.id,
                    name: attachment.name,
                    url: attachment.url,
                    cardId: card.id,
                    uploadedAt: attachment.date || new Date().toISOString()
                })) || [],
                labels: card.labels?.map(label => ({
                    _id: label.id,
                    text: label.name,
                    color: label.color,
                    enabled: true
                })) || [],
                import_type: "card"
            });
        });
    });

    return result;
};

function convertFluentBoardToZoobbe(fluentBoardData, workspaceId) {
    const { board } = fluentBoardData;

    const result = [];

    result.push({
        workspaceId: workspaceId,
        title: board.title,
        description: board.description || '',
        import_type: "board",
        labels: [],
        actionLists: [],
    });

    // Map labels
    if (board.labels) {
        zoobbeBoard.labels = board.labels.map(label => ({
            id: label.id,
            title: label.title,
            color: label.color || null,
            bg_color: label.bg_color || null,
        }));
    }

    // Map stages (action lists)
    if (board.stages) {
        board.stages.map(stage => {
            result.push({
                actionListId: stage.id,
                boardId: stage.board_id,
                title: stage.title,
                archived: stage.archived_at || false,  // Action list archived
                import_type: "actionlist"
            });
        });
    }

    // Map tasks (cards)
    if (board.tasks) {
        board.tasks.forEach(task => {
            const card = {
                id: task.id,
                title: task.title,
                description: task.description || '',
                createdAt: new Date(task.created_at),
                updatedAt: new Date(task.updated_at),
                comments: [],
                attachments: [],
                labels: [],
                members: task.assignees.map(assignee => ({
                    id: assignee.ID,
                    name: assignee.display_name,
                    email: assignee.user_email,
                    photo: assignee.photo,
                })),

                import_type: "card"
            };

            // Map comments
            if (task.comments) {
                task.comments.forEach(comment => {
                    card.comments.push({
                        id: comment.id,
                        author: comment.author_name,
                        content: comment.description || '',
                        createdAt: new Date(comment.created_at),
                    });
                });
            }

            // Find the corresponding action list and add the card
            const stage = zoobbeBoard.actionLists.find(s => s.id.toString() === task.stage_id);
            if (stage) {
                result.push(card);
            }
        });
    }

    return zoobbeBoard;
}



const createActivity = async (cardId, initiatorId, actionType, actionTypeId, details) => {
    const activity = new Activity({
        card: cardId,
        initiator: initiatorId,
        actionType: actionType,
        actionTypeId: actionTypeId,
        details: details,
    });

    await activity.save();

    // Add the activity to the card's activities array
    await Card.findOneAndUpdate({ shortId: cardId }, { $push: { activities: activity._id } });
};

// Helper function to build card filter
const buildCardFilter = (filters) => {
    const filter = {};

    if (filters.keyword) {
        const regex = new RegExp(filters.keyword, 'i');
        filter.$or = [
            { title: regex },
            { description: regex },
            { 'users.name': regex },
            { 'users.username': regex },
            { 'labels.text': regex }
        ];
    }
    if (filters.noMembers) filter.users = { $size: 0 };
    if (filters.selectedMembers?.length) filter.users = { $in: filters.selectedMembers };
    if (filters.noDates) filter['dueDate.date'] = { $exists: false };
    if (filters.overdue) filter['dueDate.date'] = { $lt: new Date() };

    if (filters.dueNextDay) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        filter['dueDate.date'] = {
            $gte: today.toISOString().split('T')[0],
            $lt: tomorrow.toISOString().split('T')[0],
        };
    }

    if (filters.noLabels) filter['labels.enabled'] = { $ne: true };
    if (filters.selectedLabels?.length) filter['labels._id'] = { $in: filters.selectedLabels };

    return filter;
};


const generateInviteToken = ({ email, shortId, role, expiresIn = null }) => {
    const payload = { email, shortId, role };
    const options = expiresIn ? { expiresIn } : {};
    const token = jwt.sign(payload, process.env.JWT_SECRET, options);
    return token;
};


const markTokenAsUsed = async (token) => {
    try {
        await UsedToken.create({ token });
    } catch (error) {
        if (error.code === 11000) {
            // Duplicate key error - token is already used
            throw new Error('Token has already been used');
        }
        throw new Error('Failed to mark token as used');
    }
};

const getUnsplashImageUrl = (url) => {
    // Extract the base URL and image name
    const [baseUrl] = url.split('?');
    const imageName = baseUrl.split('/').pop(); // Get the image name from the URL

    // Construct the new URL with the required format
    const reformattedUrl = `https://images.unsplash.com/${imageName}?fm=webp&ixlib=rb-4.0.3&q=50&w=2560`;

    return reformattedUrl;
};

const getUnsplashImageName = (url) => {
    // Extract the base URL and image name
    const [baseUrl] = url.split('?');
    const imageName = baseUrl.split('/').pop(); // Get the image name from the URL

    return imageName;
};


// Generate a SHA-256 hash for the image buffer
const generateFileHash = (buffer) => {
    return crypto.createHash('sha256').update(buffer).digest('hex');
}

const generateStringHash = (str) => {
    return crypto.createHash('sha256').update(str).digest('hex');
};

const isValidUrl = (source, url) => {
    if (!url) {
        console.log('URL is missing');
        return false; // URL is missing
    }

    try {
        const parsedUrl = new URL(url); // Validate URL format
        const hostname = parsedUrl.hostname.toLowerCase();

        // Check hostname based on the source
        switch (source.toLowerCase()) {
            case 'unsplash':
                return hostname === 'unsplash.com' || hostname.endsWith('.unsplash.com');
            case 'aws':
                return hostname === 'background-shared.s3.ap-southeast-1.amazonaws.com' || hostname.endsWith('.amazonaws.com');
            case 'cloud':
                return hostname === 'cloud.zoobbe.com' || hostname.endsWith('.zoobbe.com');
            // Add more sources here if needed
            default:
                console.log('Unsupported source:', source);
                return false; // Unsupported source
        }
    } catch (error) {
        console.error('Invalid URL format:', error.message);
        return false; // Invalid URL format
    }
};


const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
// Helper function to verify Google token
const verifyGoogleToken = async (token) => {
    try {
        const ticket = await client.verifyIdToken({
            idToken: token,
            audience: process.env.GOOGLE_CLIENT_ID,
        });
        return ticket.getPayload();
    } catch (error) {
        throw new Error('Invalid Google token');
    }
};

const getGoogleUserInfo = async (accessToken) => {
    try {
        const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error('Invalid token');
        }

        const data = await response.json();

        return data;

    } catch (error) {
        console.error('Error validating token:', error);
        return false;
    }
}



const refreshSlackToken = async (userId) => {
    try {
        const user = await User.findById(userId);
        if (!user || !user.slack?.refreshToken) {
            throw new Error("User or refresh token not found");
        }

        // ✅ Refresh 10 minutes BEFORE expiry
        if (Date.now() < user.slack.expiresAt - 10 * 60 * 1000) {
            return user.slack.botToken; // Token is still valid
        }

        console.log(`🔄 Refreshing Slack token for user: ${user.name} (ID: ${userId})`);

        // Request a new token
        const response = await fetch("https://slack.com/api/oauth.v2.access", {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: new URLSearchParams({
                client_id: process.env.SLACK_CLIENT_ID,
                client_secret: process.env.SLACK_CLIENT_SECRET,
                refresh_token: user.slack.refreshToken,
                grant_type: "refresh_token",
            }),
        });

        const data = await response.json();
        console.log("🔍 Slack API Response:", data);

        if (!data.ok || !data.access_token) {
            console.error(`❌ Failed to refresh Slack token for ${user.name}: ${data.error}`);

            // 🔴 Disconnect Slack if refresh fails
            await User.findByIdAndUpdate(userId, { $unset: { "slack": "" } });

            console.log(`🔌 Slack disconnected for ${user.name}`);
            return null;
        }

        // ✅ Update user with new token details
        user.slack.botToken = data.access_token;
        user.slack.expiresAt = Date.now() + data.expires_in * 1000;
        await user.save();

        console.log(`✅ Slack token refreshed successfully for ${user.name}`);
        return data.access_token;

    } catch (error) {
        console.error("🚨 Error refreshing Slack token:", error);
        return null;
    }
};

const refreshExpiredTokens = async () => {
    console.log("🔄 Checking for Slack tokens nearing expiration...");

    const now = Date.now();
    const users = await User.find({
        "slack.refreshToken": { $exists: true },
        "slack.expiresAt": { $lt: now + 10 * 60 * 1000 }, // Refresh 10 minutes before expiry
    }).limit(500); // Process in larger batches

    if (users.length === 0) {
        console.log("✅ No tokens needing refresh.");
        return;
    }

    console.log(`🔄 Refreshing ${users.length} Slack tokens...`);

    // ✅ Run token refreshes in parallel with a limit of 10 at a time
    const refreshQueue = [];
    for (const user of users) {
        refreshQueue.push(refreshSlackToken(user._id));

        // Limit concurrency to 10 at a time
        if (refreshQueue.length >= 10) {
            await Promise.all(refreshQueue);
            refreshQueue.length = 0; // Clear queue
        }
    }

    // Process remaining users
    if (refreshQueue.length > 0) {
        await Promise.all(refreshQueue);
    }

    console.log("🔄 Token refresh process completed.");
};


const getOrFetchSlackUserId = async (user) => {
    if (user?.slack?.userId) return user.slack.userId; // Use stored ID if available

    const slackToken = await refreshSlackToken(user._id);
    if (!slackToken) return null;

    const response = await fetch("https://slack.com/api/users.lookupByEmail", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": `Bearer ${slackToken}`,
        },
        body: new URLSearchParams({ email: user.email }),
    });

    const data = await response.json();
    if (!data.ok) {
        console.error("Error fetching Slack user ID:", data.error);
        return null;
    }

    user.slack.userId = data.user.id;
    await user.save(); // Store in DB

    return data.user.id;
};

const getOrFetchSlackChannelId = async (user) => {
    if (user?.slack?.channelId) return user.slack.channelId; // Use stored channel ID if available

    const userId = user?.slack?.userId || (await getOrFetchSlackUserId(user));
    if (!userId) return null; // If user ID isn't found, exit

    const slackToken = await refreshSlackToken(user._id);
    if (!slackToken) return null;

    const response = await fetch("https://slack.com/api/conversations.open", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${slackToken}`,
        },
        body: JSON.stringify({ users: userId }),
    });

    const data = await response.json();
    if (!data.ok) {
        console.error("Error opening DM channel:", data.error);
        return null;
    }

    user.slack.channelId = data.channel.id;
    await user.save(); // Store in DB

    return data.channel.id;
};

const sendSlackNotification = async (user, message) => {
    const channelId = await getOrFetchSlackChannelId(user);
    if (!channelId) return; // Exit if no Slack channel ID found

    const slackToken = await refreshSlackToken(user._id);
    if (!slackToken) return;

    const response = await fetch("https://slack.com/api/chat.postMessage", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${slackToken}`,
        },
        body: JSON.stringify({
            channel: channelId, // Send to stored channel ID
            text: message,
        }),
    });

    const data = await response.json();

    if (!data.ok) {
        console.error("Slack API error:", data.error);
    }
};


const sendAppNotification = async ({
    initiatorId,
    recipients = [],
    targetType = 'Card',
    targetId,
    actionType,
    message,
    io
}) => {
    // Create notifications for the specified members
    const notifications = await Notification.insertMany(
        recipients.map(memberId => ({
            member: memberId,
            initiator: initiatorId,
            type: actionType,
            targetType: targetType,
            targetId: targetId,
            message: message,
        }))
    );

    // Populate the initiator data for notifications
    const populatedNotifications = await Notification.populate(notifications, {
        path: 'initiator',
        select: 'name username email profilePicture'
    });

    // Emit the notification to each member
    populatedNotifications.forEach(notification => {
        io.to(notification.member.toString()).emit('newNotification', notification);
    });
};


const sendPushNotificationHandler = async ({ memberId, type, data, initiator }) => {
    try {
        const { cardId, sourceListTitle = '', targetListTitle = '', username } = data;

        if (!memberId || !type) {
            console.error("Missing required fields: 'memberId' and 'type' are required.");
            return;
        }

        const card = await Card.findOne({ shortId: cardId });
        if (!card) {
            console.error("Card not found.");
            return;
        }

        const user = await User.findById(memberId);
        if (!user) {
            console.error("User not found.");
            return;
        }

        const tokens = user.pushTokens || [];
        const url = card.permalink || "https://zoobbe.com"; // Fallback URL

        const isSendSlackNotification = (user.slack?.connected && user.settings?.slackNotifications) || false;
        const isSendPushNotification = user.settings?.pushNotifications || false;

        let title = "Zoobbe Notification";
        let simpleBody = "You have a new update."; // For push notifications
        let slackBody = simpleBody; // Default Slack message

        const cardLink = `${process.env.SITE_URL}${card.shortLink}`;
        const initiatorProfile = `${process.env.SITE_URL}/u/${username}/activity`;

        const notificationMessages = {
            ADD_MEMBER_TO_CARD: [
                "New Card Assigned",
                `${initiator} added you to a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* added you to the card *<${cardLink}|${card.title}>*.`
            ],
            REMOVE_MEMBER_FROM_CARD: [
                "Removed from Card",
                `${initiator} removed you from a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* removed you from the card *<${cardLink}|${card.title}>*.`
            ],
            COMMENT_ON_CARD: [
                "New Comment",
                `${initiator} left a comment on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* commented on *<${cardLink}|${card.title}>*.`
            ],
            MENTION_IN_COMMENT: [
                "You Were Mentioned",
                `${initiator} mentioned you in a comment on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* mentioned you in a comment on *<${cardLink}|${card.title}>*.`
            ],
            MENTION_IN_CARD: [
                "You Were Mentioned",
                `${initiator} mentioned you in a card update on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* mentioned you in a card update on *<${cardLink}|${card.title}>*.`
            ],
            DUE_DATE_REMINDER: [
                "Card Due Soon",
                `Reminder: You have a card due soon on Zoobbe.`,
                `Reminder: Your card *<${cardLink}|${card.title}>* is due soon.`
            ],
            CARD_MOVED: [
                "Card Updated",
                `${initiator} moved a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* moved a *<${cardLink}|card>* from ${sourceListTitle} to ${targetListTitle || 'another list'}`,
            ],
            CARD_REPOSITIONED: [
                "Card Updated",
                `${initiator} repositioned a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* repositioned *<${cardLink}|${card.title}>* in *${sourceListTitle}*.`
            ],
            TASK_COMPLETED: [
                "Card Completed",
                `${initiator} marked a card as done on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* marked *<${cardLink}|${card.title}>* as done.`
            ],
            CARD_ARCHIVED: [
                "Card Updated",
                `${initiator} closed a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* closed *<${cardLink}|${card.title}>*.`
            ],
            CARD_UNARCHIVED: [
                "Card Updated",
                `${initiator} re open a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* re open *<${cardLink}|${card.title}>*.`
            ],
            NEW_ASSIGNMENT: [
                "New Assignment",
                `${initiator} assigned you a card on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* assigned you to *<${cardLink}|${card.title}>*.`
            ],
            PROJECT_INVITATION: [
                "Project Invitation",
                `${initiator} invited you to a project on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* invited you to a project on Zoobbe.`
            ],
            PROJECT_REMOVAL: [
                "Removed from Project",
                `${initiator} removed you from a project on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* removed you from a project.`
            ],
            NEW_MESSAGE: [
                "New Message",
                `${initiator} sent you a message on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* sent you a message.`
            ],
            REACTION_TO_COMMENT: [
                "Reaction on Comment",
                `${initiator} reacted to your comment on Zoobbe.`,
                `*<${initiatorProfile}|${initiator}>* reacted to your comment.`
            ]
        };

        if (notificationMessages[type]) {
            [title, simpleBody, slackBody] = notificationMessages[type];
        }

        // Send push notification
        if (isSendPushNotification && tokens.length > 0) {
            await sendPushNotification({ tokens, title, body: simpleBody, url }).catch((err) => {
                console.error("Push notification failed:", err);
            });
        }

        // Send Slack notification
        if (isSendSlackNotification) {
            await sendSlackNotification(user, slackBody).catch((err) => {
                console.error("Slack notification failed:", err);
            });
        }

        console.log("Notifications sent successfully.");
    } catch (error) {
        console.error("Push notification error:", error);
    }
};


// const sendSlackNotification = async (channel, message) => {
//     const slackToken = process.env.SLACK_BOT_TOKEN; // Store securely

//     const response = await fetch("https://slack.com/api/chat.postMessage", {
//         method: "POST",
//         headers: {
//             "Content-Type": "application/json",
//             "Authorization": `Bearer ${slackToken}`,
//         },
//         body: JSON.stringify({
//             channel: channel,
//             text: message,
//         }),
//     });

//     const data = await response.json();

//     if (!data.ok) {
//         console.error("Slack API error:", data.error);
//     }
// };


const getFileType = (mimetype) => {
    if (mimetype.startsWith('image/')) return 'IMAGE';
    if (mimetype.startsWith('video/')) return 'VIDEO';
    if (mimetype === 'application/pdf') return 'PDF';
    if (mimetype === 'application/zip' || mimetype === 'application/x-zip-compressed') return 'ZIP';
    return mimetype; // Default type
};

const FILE_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 1; //1 day
const AUTH_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 30; //1 days

const STRIPE_PRICES = {
    Standard: {
        monthly: {
            id: 'price_1RHOgxK0cSs6yAeTY4mwEMMZ',
            amount: 499  // in cents
        },
        annually: {
            id: 'price_1RHOgxK0cSs6yAeT38dhronu',
            amount: 4999 // in cents
        }
    },
    Premium: {
        monthly: {
            id: 'price_1RHOkkK0cSs6yAeTFtoiFIZi',
            amount: 999  // in cents
        },
        annually: {
            id: 'price_1RHOkkK0cSs6yAeT3u6MgEM6',
            amount: 9599 // in cents
        }
    }
};

const SUBSCRIPTION_LIMITS = {
    Free: 1,
    Standard: Infinity,
    Premium: Infinity,
    Enterprise: Infinity,
};

const validatePriceId = (priceId) => {
    return Object.values(STRIPE_PRICES)
        .flatMap(plan => Object.values(plan))
        .flatMap(cycle => cycle.id)
        .includes(priceId);
};

const calculateAmount = (plan, billingCycle, userCount) => {
    if (!STRIPE_PRICES[plan] || !STRIPE_PRICES[plan][billingCycle]) {
        throw new Error('Invalid plan or billing cycle');
    }
    return STRIPE_PRICES[plan][billingCycle].amount * userCount;
};

const calculateProratedAmount = (plan, billingCycle, userCount, startDate, endDate) => {
    if (!STRIPE_PRICES[plan] || !STRIPE_PRICES[plan][billingCycle]) {
        throw new Error('Invalid plan or billing cycle');
    }

    // Calculate total days in billing cycle
    const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24);

    // Calculate days remaining in billing cycle
    const now = new Date();
    const daysRemaining = (endDate - now) / (1000 * 60 * 60 * 24);

    // If no days remaining or negative, return 0
    if (daysRemaining <= 0) {
        return 0;
    }

    // Calculate full amount
    const fullAmount = STRIPE_PRICES[plan][billingCycle].amount * userCount;

    // Calculate prorated amount (rounded to nearest cent)
    return Math.round((daysRemaining / totalDays) * fullAmount);
};

module.exports = {
    emitUserAction,
    emitIOCardAction,
    convertTrelloExportToZoobbe,
    convertFluentBoardToZoobbe,
    createActivity,
    buildCardFilter,
    generateInviteToken,
    refreshExpiredTokens,
    markTokenAsUsed,
    getUnsplashImageUrl,
    getUnsplashImageName,
    generateFileHash,
    generateStringHash,
    isValidUrl,
    verifyGoogleToken,
    getGoogleUserInfo,
    sendSlackNotification,
    sendPushNotificationHandler,
    sendAppNotification,
    getFileType,
    FILE_EXPIRATION_TIME,
    AUTH_EXPIRATION_TIME,
    STRIPE_PRICES,
    SUBSCRIPTION_LIMITS,
    validatePriceId,
    calculateAmount,
    calculateProratedAmount,
}
