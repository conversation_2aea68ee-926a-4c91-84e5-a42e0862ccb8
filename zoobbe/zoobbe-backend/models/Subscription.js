const mongoose = require('mongoose');
const { Schema } = mongoose;

const subscriptionSchema = new Schema({
    //Stripe schemma
    workspaceId: { type: Schema.Types.ObjectId, ref: 'Workspace', required: true },
    status: { type: String, enum: ['active', 'canceled', 'past_due', 'incomplete', 'incomplete_expired', 'trialing', 'unpaid'], default: 'incomplete' },
    planType: { type: String, enum: ['Free', 'Standard', 'Premium', 'Enterprise'], default: 'Free' },
    billingCycle: { type: String, enum: ['monthly', 'annually'], default: 'annually' },
    priceId: String,
    quantity: { type: Number, default: 1 },
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    cancelAtPeriodEnd: { type: Boolean, default: false },
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    lastPaymentDate: Date,
    nextPaymentDate: Date,
    canceledAt: Date,
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },

    //workspace member schema
    reservedSeats: { type: Number, default: 0 },

    // Pro status tracking
    isPermanentlyPro: { type: Boolean, default: false },

    // Seat management
    pendingSeats: { type: Number, default: 0 },
    removedSeats: { type: Number, default: 0 }
});

// Update timestamps
subscriptionSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});

// Indexes
subscriptionSchema.index({ workspaceId: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ stripeCustomerId: 1 });
subscriptionSchema.index({ stripeSubscriptionId: 1 });

module.exports = mongoose.models.Subscription || mongoose.model('Subscription', subscriptionSchema);
