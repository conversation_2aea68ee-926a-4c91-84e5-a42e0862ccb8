require('dotenv').config();
const express = require('express');
const cors = require('cors');
const cron = require('node-cron');
const cookieParser = require('cookie-parser');
const path = require('path');
const http = require('http');
const connectDB = require('./config/db');
const bodyParser = require('body-parser');
const { S3Client } = require("@aws-sdk/client-s3");
const userRoutes = require('./routes/userRoutes');
const workspaceRoutes = require('./routes/workspaceRoutes');
const boardRoutes = require('./routes/boardRoutes');
const cardRoutes = require('./routes/cardRoutes');
const actionListRoutes = require('./routes/actionListRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const memberRoutes = require('./routes/memberRoute');
const feedbackRoutes = require('./routes/feedbackRoutes');
const stripeRoutes = require('./routes/stripeRoutes');

const setupSocket = require('./socket'); // Import setupSocket



const { backfill, migrateToCloudFront, migrateProfilePictures, migrateCardAttachments } = require('./migration');
const { refreshExpiredTokens } = require('./utils/helper');

const app = express();
const PORT = process.env.PORT || 5000;

const s3 = new S3Client({
  region: process.env.BUCKET_REGION,
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY,
    secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
  },
});

const corsOptions = {
  origin: [
    process.env.SITE_URL,
    'https://zoobbe.com',
    'http://zoobbe.com',
    'https://www.zoobbe.com',
    'https://beta.zoobbe.com',
    'https://www.beta.zoobbe.com',
    'http://www.zoobbe.com',
    'http://localhost:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  optionsSuccessStatus: 200,
};


app.use('/api/stripe/webhook', express.raw({ type: 'application/json' }));

// Use cookie-parser middleware before your routes
app.use(cookieParser());

app.use(cors(corsOptions));
app.options('*', cors(corsOptions));
app.use(express.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

connectDB();

// Health check route
app.get('/healthcheck', (req, res) => {
  res.status(200).json({ status: 'OK' });
});

// Set up routes
app.use('/api/users', userRoutes);
app.use('/api', workspaceRoutes, boardRoutes, actionListRoutes, cardRoutes, notificationRoutes, memberRoutes, feedbackRoutes);
app.use('/api/stripe', stripeRoutes);


// Create an HTTP server
const server = http.createServer(app);

// Initialize Socket.IO using the setupSocket function
const io = setupSocket(server);
app.set('socketio', io);

// Start the server
server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

// Error handling middleware
app.use((err, req, res, next) => {
  res.status(500).json({ message: 'Internal Server Error' });

});

// ✅ Run every 30 minutes instead of 320 minutes (5.3 hours)
cron.schedule("*/30 * * * *", async () => {
  console.log("⏳ Running scheduled Slack token refresh...");
  await refreshExpiredTokens();
});


// backfill();
// migrateToCloudFront();
// migrateProfilePictures();
// migrateCardAttachments();


// If needed, export io for use in other modules
module.exports = { app, s3 };
