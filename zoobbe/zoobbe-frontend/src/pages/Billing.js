// src/components/Billing.js

import { useDispatch, useSelector } from "react-redux";
import { config } from "../config";
import { stripePromise } from "../utils/stripe";

import './scss/BillingTable.scss';
import { useEffect, useState } from "react";
import Checkout from "./Checkout";
import { fetchWorkspaces } from "../redux/Slices/workspaceSlice";
import { useNavigate, useParams } from "react-router-dom";
import { find } from "../utils/helpers";
import { fetchMembers } from "../redux/Slices/memberSlice";

const plans = [
    {
        name: 'Free',
        description: 'Perfect for individuals and small teams getting started with Zoobbe.',
        prices: { monthly: '$0 USD', annually: '$0 USD' },
        subTexts: { monthly: 'Free forever', annually: 'Free forever' },
        features: [
            'Unlimited cards',
            'Boards: Up to 15',
            'Collaborators: 15',
            'File attachments: 20 MB per file',
            'Storage: 512 MB per workspace',
            'Basic checklists',
            'Mentions & real-time notifications',
            'Custom backgrounds & stickers',
            'Last activity status for all members'
        ]
    },
    {
        name: 'Standard',
        description: 'More power for growing teams: organize, customize, and collaborate better.',
        prices: { monthly: '$4.99 USD', annually: '$49.99 USD' },
        pricesId: { monthly: 'price_1RHOgxK0cSs6yAeTY4mwEMMZ', annually: 'price_1RHOgxK0cSs6yAeT38dhronu' },
        subTexts: { monthly: 'per seat / month', annually: 'per seat / year' },
        cta: 'Upgrade now',
        features: [
            'Unlimited cards',
            'Boards: Unlimited',
            'Collaborators: Unlimited',
            'File attachments: 250 MB per file',
            'Storage: Unlimited',
            'Saved searches',
            'Advanced checklists',
            'List colors',
            'Board backgrounds & stickers',
            'Last activity status for all members',
            'Online status visibility (if member of any Pro workspace)',
            'Pro verified badge for Pro workspace members'
        ]
    },
    {
        name: 'Premium',
        description: 'For teams that need insights, controls, and more collaboration power.',
        prices: { monthly: '$9.99 USD', annually: '$95.99 USD' },
        pricesId: { monthly: 'price_1RHOkkK0cSs6yAeTFtoiFIZi', annually: 'price_1RHOkkK0cSs6yAeT3u6MgEM6' },
        subTexts: { monthly: 'per seat / month', annually: 'per seat / year' },
        cta: 'Upgrade now',
        features: [
            'Unlimited cards',
            'Boards: Unlimited',
            'Collaborators: Unlimited',
            'File attachments: 250 MB per file',
            'Storage: Unlimited',
            'Saved searches',
            'Advanced checklists',
            'List colors',
            'Board backgrounds & stickers',
            'Custom stickers & emojis',
            'Data export (JSON/CSV)',
            'Extended activity logs',
            'Priority support',
            'Last activity status for all members',
            'Online status visibility (if member of any Pro workspace)',
            'Pro verified badge for Pro workspace members'
        ]
    },
    {
        name: 'Enterprise',
        description: 'Enterprise-grade security, support, and scale. Tailored for large organizations.',
        prices: {
            monthly: 'Contact sales',
            annually: 'Billing varies with number of users'
        },
        subTexts: { monthly: '', annually: '' },
        cta: 'Contact sales',
        features: [
            'Unlimited cards',
            'Boards: Unlimited',
            'Collaborators: Unlimited',
            'File attachments: 250 MB per file',
            'Storage: Unlimited',
            'Saved searches',
            'Advanced checklists',
            'List colors',
            'Board backgrounds & stickers',
            'Custom stickers & emojis',
            'Data export (JSON/CSV)',
            'Extended activity logs',
            'Priority support',
            'Dedicated support contact',
            'Attachment restrictions',
            'User role management',
            'Organization-wide permissions',
            'Public board visibility controls',
            'SSO (Coming Soon)',
            'Custom security policies',
            'Last activity status for all members',
            'Online status visibility (if member of any Pro workspace)',
            'Pro verified badge for Pro workspace members'
        ]
    }
];



function Billing() {

    const { user } = useSelector((state) => state.user);
    const [isYearly, setIsYearly] = useState(true);
    const [showCheckout, setShowCheckout] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [billingStatus, setBillingStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    const { shortId } = useParams();
    const dispatch = useDispatch();

    const members = useSelector(state => state.member.workspaceMembers);
    const guests = useSelector(state => state.member.workspaceGuests);

    const userCount = members?.length + guests?.length;

    const navigate = useNavigate();

    // Fetch billing status
    useEffect(() => {
        const fetchBillingStatus = async () => {
            try {
                setIsLoading(true);
                const res = await fetch(config.API_URI + `/api/stripe/billing-status/${shortId}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                });

                const data = await res.json();
                setBillingStatus(data);
            } catch (error) {
                console.error('Error fetching billing status:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchBillingStatus();
    }, [shortId]);

    useEffect(() => {
        dispatch(fetchMembers({ type: 'workspace', id: shortId }));
        dispatch(fetchMembers({ type: 'guests', id: shortId }));
    }, [dispatch, shortId]);


    const handleSubscribe = async (planId) => {
        const stripe = await stripePromise;

        const res = await fetch(config.API_URI + '/api/stripe/create-checkout-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ planId, userId: user.user._id }) // userId from auth
        });

        const data = await res.json();

        if (data.url) {
            window.location.href = data.url; // Redirect to Stripe Checkout
        } else {
            alert("Something went wrong.");
        }
    };

    const [billing, setBilling] = useState('annually');

    const handleCTAClick = (plan) => {
        const selectedPriceId = plan.pricesId[billing]; // Get the correct price ID based on billing cycle
        setShowCheckout(true);
        setSelectedPlan({
            ...plan,
            priceId: selectedPriceId // Add the price ID to the plan object
        });
    };
    const handleNavigate = () => {
        navigate('/');
    };



    return (
        <>

            {
                !showCheckout && (
                    <div className="billing-container">
                        <div className="close-button" onClick={() => handleNavigate()}>
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e3e3e3"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" /></svg>
                        </div>

                        {isLoading ? (
                            <div className="loading-container">
                                <p>Loading billing information...</p>
                            </div>
                        ) : billingStatus && (billingStatus.status === 'active' || billingStatus.isPermanentlyPro) ? (
                            <div className="billing-status-container">
                                <h2>Current Subscription</h2>
                                <div className="billing-status-details">
                                    <div className="billing-status-item">
                                        <span className="label">Plan:</span>
                                        <span className="value">{billingStatus.planType}</span>
                                    </div>
                                    <div className="billing-status-item">
                                        <span className="label">Billing Cycle:</span>
                                        <span className="value">{billingStatus.billingCycle === 'annually' ? 'Annual' : 'Monthly'}</span>
                                    </div>
                                    <div className="billing-status-item">
                                        <span className="label">Status:</span>
                                        <span className="value status-badge">{billingStatus.status}</span>
                                    </div>
                                    <div className="billing-status-item">
                                        <span className="label">Next Billing Date:</span>
                                        <span className="value">{billingStatus.nextBillingDate ? new Date(billingStatus.nextBillingDate).toLocaleDateString() : 'N/A'}</span>
                                    </div>
                                </div>

                                <h3>Seat Management</h3>
                                <div className="seat-management">
                                    <div className="seat-status-item">
                                        <span className="label">Reserved Seats:</span>
                                        <span className="value">{billingStatus.reservedSeats}</span>
                                    </div>
                                    <div className="seat-status-item">
                                        <span className="label">Current Members:</span>
                                        <span className="value">{billingStatus.currentMemberCount}</span>
                                    </div>
                                    <div className="seat-status-item">
                                        <span className="label">Available Seats:</span>
                                        <span className="value">{billingStatus.availableSeats}</span>
                                    </div>
                                    {billingStatus.pendingSeats > 0 && (
                                        <div className="seat-status-item pending">
                                            <span className="label">Pending Seats:</span>
                                            <span className="value">{billingStatus.pendingSeats}</span>
                                            <button
                                                className="add-seats-button"
                                                onClick={() => {
                                                    setSelectedPlan({
                                                        name: billingStatus.planType,
                                                        priceId: billingStatus.priceId
                                                    });
                                                    setBilling(billingStatus.billingCycle);
                                                    setShowCheckout(true);
                                                }}
                                            >
                                                Pay for pending seats
                                            </button>
                                        </div>
                                    )}
                                </div>

                                <div className="add-seats-section">
                                    <h3>Need more seats?</h3>
                                    <button
                                        className="add-seats-button"
                                        onClick={() => {
                                            setSelectedPlan({
                                                name: billingStatus.planType,
                                                priceId: billingStatus.priceId
                                            });
                                            setBilling(billingStatus.billingCycle);
                                            setShowCheckout(true);
                                        }}
                                    >
                                        Add more seats
                                    </button>
                                </div>
                            </div>
                        ) : (
                            <>
                                <p className="trial-note">Choose a plan to upgrade your workspace.</p>

                                <div className="billing-toggle">
                                    <button className={billing === 'monthly' ? 'active' : ''} onClick={() => setBilling('monthly')}>Monthly</button>
                                    <button className={billing === 'annually' ? 'active' : ''} onClick={() => setBilling('annually')}>
                                        Annually <span className="discount">SAVE 17%</span>
                                    </button>
                                </div>

                                <div className="billing-cards">
                                    {plans.map((plan) => (
                                        <div className={`card ${plan.name.toLowerCase()}`} key={plan.name}>
                                            <h2>{plan.name}</h2>
                                            <div className="description">{plan.description}</div>
                                            <div className="price">{plan.prices && plan.prices[billing]}</div>
                                            <div className="sub-text">{plan.subTexts && plan.subTexts[billing]}</div>
                                            {plan.cta && <button className="cta-button" onClick={() => handleCTAClick(plan)}><span>{plan.cta}</span></button>}
                                            <ul className="features">
                                                {plan.features.map((feature, idx) => (
                                                    <li key={idx}>
                                                        <span className="check">
                                                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#a855f7"><path d="M382-240 154-468l57-57 171 171 367-367 57 57-424 424Z" /></svg>
                                                        </span>
                                                        <span className="feature-text">{feature}</span></li>
                                                ))}
                                            </ul>
                                        </div>
                                    ))}
                                </div>
                            </>
                        )}
                    </div>
                )
            }


            {
                showCheckout && (
                    <div className="checkout-page">
                        <div className="close-button" onClick={() => setShowCheckout(false)}>
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e3e3e3"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" /></svg>
                        </div>
                        <Checkout plan={selectedPlan} billing={billing} userCount={userCount} />
                    </div>
                )
            }
        </>
    );
}

export default Billing;
