// BillingTable.scss

$brandColor: var(--brand-color);

.close-button {
    position: absolute;
    margin: 20px;
    right: 0;
    top: 0;
    display: inline-block;
    cursor: pointer;

    svg {
        fill: var(--brand-color);
    }
}

.billing-container {
    // background-color: #0c0c0e;
    padding: 3rem 2rem;
    color: var(--primary-text-color);
    font-family: "Inter", sans-serif;
    text-align: center;

    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .billing-status-container {
        max-width: 800px;
        margin: 0 auto;
        text-align: left;

        h2 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary-text-color);
        }

        h3 {
            font-size: 1.2rem;
            margin: 2rem 0 1rem;
            color: var(--primary-text-color);
        }

        .billing-status-details, .seat-management {
            background: var(--workspace-icon-background-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--price-table-border);
        }

        .billing-status-item, .seat-status-item {
            display: flex;
            margin-bottom: 1rem;
            align-items: center;

            &:last-child {
                margin-bottom: 0;
            }

            &.pending {
                background: rgba(255, 165, 0, 0.1);
                padding: 1rem;
                border-radius: 8px;
                margin-top: 1rem;
                border: 1px solid rgba(255, 165, 0, 0.3);
            }

            .label {
                font-weight: 500;
                width: 150px;
                flex-shrink: 0;
            }

            .value {
                flex-grow: 1;

                &.status-badge {
                    display: inline-block;
                    padding: 0.25rem 0.75rem;
                    border-radius: 12px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    background: var(--brand-color);
                    color: white;
                }
            }

            .add-seats-button {
                margin-left: auto;
                background: var(--brand-color);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 0.5rem 1rem;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.3s;

                // &:hover {
                //     background: darken($brandColor, 10%);
                // }
            }
        }

        .add-seats-section {
            text-align: center;
            margin-top: 2rem;

            .add-seats-button {
                background: var(--brand-color);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 0.75rem 1.5rem;
                font-weight: 500;
                font-size: 1rem;
                cursor: pointer;
                transition: background 0.3s;

                // &:hover {
                //     background: darken($brandColor, 10%);
                // }
            }
        }
    }

    .billing-toggle {
        display: inline-flex;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 4rem;

        button {
            background: transparent;
            color: var(--primary-text-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;

            &.active {
                background: var(--price-table-border);
                color: var(--primary-text-color);

                .discount {
                    background: #4f46e5;
                    color: white;
                    padding: 2px 6px;
                    font-size: 0.75rem;
                    border-radius: 6px;
                    margin-left: 6px;
                }
            }
        }
    }

    .billing-cards {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;

        .card {
            border-radius: 2rem;
            padding: 2rem;
            width: 250px;
            text-align: left;
            border: 0.6px solid var(--price-table-border);
            // height: max-content;
            h2 {
                font-size: 1.25rem;
                font-weight: 500;
                margin-bottom: 1rem;
                margin-top: 0;
                color: var(--primary-text-color);
            }

            .description {
                font-size: 0.875rem;
                color: #a1a1aa;
                margin-bottom: 1.5rem;
                line-height: 1.6;
            }
            .sub-text {
                margin-bottom: 15px;
                color: var(--primary-text-color);
            }

            .price {
                font-size: 2.2rem;
                font-weight: 700;
                margin-bottom: 1.5rem;
                color: var(--primary-text-color);
            }

            .cta-button {
                border-radius: 4px 4px 1.2rem;
                border: none;
                font-weight: 600;
                font-size: 12px;
                cursor: pointer;
                margin-bottom: 1.5rem;
                text-transform: uppercase;
                transition: background 0.3s;
                width: 100%;
                margin-top: 15px;
                height: 40px;
                cursor: pointer;

                &:hover {
                    background: #e5e5e5;
                }
            }

            &.enterprise .cta-button {
                background: linear-gradient(90deg, #7f00ff, #e100ff);
                color: white;
                border: 1px solid #7f00ff;
            }

            .features {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    display: flex;
                    font-size: 0.875rem;
                    margin-bottom: 8px;
                    color: #e4e4e7;
                    line-height: 1.6;

                    .check {
                        margin-right: 0.5rem;
                        display: inline-flex;
                        align-items: center;
                        height: 16px;
                        width: 16px;
                        border-radius: 50px;
                        justify-content: center;
                        margin-top: 3px;

                        svg {
                            height: 14px;
                            width: 14px;
                            fill: var(--brand-color);
                        }
                    }

                    .feature-text {
                        width: calc(100% - 16px);
                        color: var(--primary-text-color);
                    }
                }
            }
        }

        .card.premium {
            h2 {
                color: #b480ea;
            }
            margin-top: -20px;
            border: 1px solid var(--brand-color) !important;

            button.cta-button {
                position: relative;
                display: inline-block;
                color: var(--primary-text-color);
                background: linear-gradient(90deg, #f0c464, #ffc09f, #ffafcc, #89cff0, #cdb4db, #a1c4fd, #b493d4);
                font-weight: 600;
                cursor: pointer;
                overflow: hidden;
            }

            button.cta-button::before {
                content: "";
                position: absolute;
                top: 1px;
                bottom: 1px;
                left: 1px;
                right: 1px;
                background: var(--workspace-background-color); // or any color that matches your card background
                border-radius: 4px 4px 1.2rem; // Slightly smaller to match inner corner
                z-index: 1;
            }

            button.cta-button span {
                position: relative;
                z-index: 2;
            }
        }

        .card.standard h2 {
            color: #f5d79d;
        }
        .card.enterprise h2 {
            color: #ef8a85;
        }
    }
}
