// CheckoutForm.scss
.checkout-container {
    display: flex;
    max-width: 1100px;
    margin: 40px auto;
    font-size: 14px;
    color: var(--white-text-color-alternative);
    line-height: 1.6em;
}

.board-header {
    display: flex;
    align-items: center;
    /* background-color: var(--single-card-side-action-bg-color); */
    padding: 10px;
    /* border-radius: 8px; */
    color: var(--white-text-color-alternative);
    max-width: 1100px;
    padding: 20px 20px;
    margin: auto;
    border-bottom: 1px solid var(--outline-color);
    padding-top: 60px;

    .board-icon {
        background-color: #f05a28;
        border-radius: var(--element-border-radius);
        width: 48px;
        height: 48px;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-text {
            color: var(--white-text-color-alternative);
            font-size: 24px;
            font-weight: bold;
        }
    }

    .board-details {
        margin-left: 10px;

        .board-name {
            display: flex;
            align-items: center;

            span {
                font-size: 20px;
                font-weight: 600;
            }

            span.material-symbols-outlined {
                margin-left: 5px;
                cursor: pointer;
                color: var(--single-card-text-color);
                transform: rotate(-15deg);
            }
        }

        .board-visibility {
            display: flex;
            align-items: center;
            margin-top: 5px;

            span.material-symbols-outlined {
                font-size: 14px !important;
                margin-right: 4px;
            }

            span {
                font-size: 14px;
                color: var(--single-card-text-color);
            }
        }
    }
}

.checkout-left {
    width: 280px;
    padding: 0 24px;

    h2 {
        font-size: 18px;
        font-weight: bold;
        margin-top: 0;
        line-height: 1.4;
    }

    .feature-intro {
        margin-top: 16px;
        font-weight: 500;
    }

    .features-list {
        list-style: none;
        margin: 16px 0;
        padding: 0;

        li {
            position: relative;
            display: flex;
            font-size: 0.875rem;
            margin-bottom: 8px;
            color: var(--white-text-color-alternative);
            line-height: 1.6;

            .check {
                margin-right: 0.5rem;
                display: inline-flex;
                align-items: center;
                height: 16px;
                width: 16px;
                border-radius: 50px;
                justify-content: center;
                margin-top: 3px;
                svg {
                    height: 14px;
                    width: 14px;
                    fill: var(--brand-color);
                }
            }
        }
    }

    .compare-link {
        display: block;
        margin: 16px 0;
        color: #0052cc;
        font-weight: 500;
        text-decoration: none;
    }

    .support-links {
        margin-top: 24px;

        a {
            display: block;
            color: #6b778c;
            font-size: 12px;
            margin-bottom: 4px;
        }
    }
}

.checkout-right {
    width: calc(100% - 280px);
    padding: 0 24px;
    border-right: 1px solid var(--outline-color);

    h3 {
        font-size: 16px;
        margin-bottom: 16px;
        padding: 0;
        margin-top: 0;
    }

    .plan-options {
        display: flex;
        gap: 12px;

        .plan {
            flex: 1;
            outline: 1px solid var(--outline-color);
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;

            &.selected {
                outline: 2px solid var(--brand-color);
                background-color: var(--select-option-hover-background);
            }

            .plan-name {
                font-weight: bold;
                margin-bottom: 4px;
            }

            .plan-price {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 5px;

                span {
                    font-size: 12px;
                    margin-left: 4px;
                }
            }

            .plan-note {
                font-size: 12px;
                line-height: 1.6;
            }
        }
    }

    .step-label {
        margin: 24px 0 8px;
        font-weight: bold;
        font-size: 12px;
        color: #6b778c;
    }

    .payment-form {
        label {
            display: flex;
            margin-bottom: 16px;
            flex-direction: column;
            gap: 5px;
            font-weight: 500;

            input,
            .stripe-input {
                margin-top: 4px;
                width: 100%;
                padding: 0 12px;
                outline: 1px solid var(--input-border-color);
                border-radius: 2px;
                height: 35px;
                border: none;
                font-size: 14px;
                font-weight: 500;
                font-family: system-ui;
                background-color: transparent;
                color: var(--white-text-color-alternative);
                width: calc(100% - 20px);

                .StripeElement{
                    margin-top: 10px;
                }

                &:focus {
                    outline: 2px solid var(--brand-color);
                }
            }
            :not(.search-select).focused {
                outline: 2px solid var(--brand-color);
            }


            .search-select-trigger {
                margin-top: 4px;
                background-color: transparent;

                input {
                    outline: none;
                    margin: 0;
                }
            }
        }

        .input-row {
            display: flex;
            gap: 12px;

            label {
                flex: 1;
            }
        }
    }

    .billing-summary {
        margin-top: 24px;

        .seats-to-add {
            margin-bottom: 16px;

            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            .seats-input {
                display: flex;
                align-items: center;

                button {
                    width: 32px;
                    height: 32px;
                    background: var(--workspace-icon-background-color);
                    border: 1px solid var(--input-border-color);
                    color: var(--white-text-color-alternative);
                    font-size: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    &:first-child {
                        border-radius: 4px 0 0 4px;
                    }

                    &:last-child {
                        border-radius: 0 4px 4px 0;
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }

                input {
                    width: 60px;
                    height: 32px;
                    border: 1px solid var(--input-border-color);
                    border-left: none;
                    border-right: none;
                    background: transparent;
                    color: var(--white-text-color-alternative);
                    text-align: center;
                    font-size: 14px;

                    &::-webkit-inner-spin-button,
                    &::-webkit-outer-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }
                }
            }
        }

        .summary-note {
            font-size: 12px;
            color: var(--single-card-text-color);
            margin-bottom: 16px;
            font-style: italic;
        }

        .summary-item,
        .summary-total {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .summary-total {
            font-weight: bold;
            margin-top: 16px;
        }

        .billing-toggle {
            display: flex;
            align-items: center;
            margin-top: 16px;

            .toggle {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-left: 8px;

                .option {
                    font-size: 12px;
                    color: #6b778c;
                }

                .option.active {
                    color: var(--brand-color);
                    font-weight: bold;
                }

                .switch {
                    width: 36px;
                    height: 17px;
                    border: 1px solid var(--brand-color);
                    border-radius: 999px;
                    position: relative;
                    transition: all 0.3s;
                    cursor: pointer;

                    &.on::after {
                        content: "";
                        position: absolute;
                        top: 2.3px;
                        left: 20px;
                        width: 14px;
                        height: 14px;
                        background: var(--brand-color);
                        border-radius: 50%;
                        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
                        transition: all 0.3s;
                        top: 50%;
                        transform: translate(0px, -50%);
                    }
                    &.off::after {
                        content: "";
                        position: absolute;
                        top: 2.3px;
                        right: 20px;
                        width: 14px;
                        height: 14px;
                        background: var(--brand-color);
                        border-radius: 50%;
                        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
                        transition: all 0.3s;
                        top: 50%;
                        transform: translate(0px, -50%);
                    }
                }
            }
        }
    }

    .pending-invite {
        background: var(--select-option-hover-background);
        padding: 12px;
        border-radius: 4px;
        margin-top: 24px;
        font-size: 13px;

        p {
            color: var(--white-text-color-alternative);
            line-height: 1.6;
            margin: 0;
        }
    }

    .confirm-btn {
        display: block;
        background: var(--brand-color);
        color: white;
        font-weight: 500;
        padding: 10px 20px;
        border: none;
        margin-top: 16px;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background: var(--hover-brand-color);
        }
    }
}

.checkout-loading{
    color: var(--primary-text-color);
}