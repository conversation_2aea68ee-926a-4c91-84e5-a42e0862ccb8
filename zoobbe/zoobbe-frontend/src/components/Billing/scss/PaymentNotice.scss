.payment-notice-container {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  padding: 0 16px;
  
  .payment-notice {
    max-width: 800px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin-top: 8px;
    
    &.admin-notice {
      background-color: var(--workspace-icon-background-color);
      border: 1px solid #ff9800;
    }
    
    &.member-notice {
      background-color: var(--workspace-icon-background-color);
      border: 1px solid #ff9800;
    }
    
    .notice-content {
      display: flex;
      align-items: center;
      flex: 1;
      
      .notice-icon {
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .notice-text {
        color: var(--primary-text-color);
        font-size: 14px;
        
        p {
          margin: 0;
        }
        
        strong {
          font-weight: 600;
        }
      }
    }
    
    .notice-actions {
      display: flex;
      align-items: center;
      margin-left: 16px;
      
      .pay-button {
        background-color: var(--brand-color);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        text-decoration: none;
        margin-right: 8px;
        
        &:hover {
          background-color: darken(#a855f7, 5%);
        }
      }
      
      .dismiss-button {
        background-color: transparent;
        color: var(--primary-text-color);
        border: 1px solid var(--outline-color);
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        
        &:hover {
          background-color: var(--select-option-hover-background);
        }
      }
    }
  }
}
