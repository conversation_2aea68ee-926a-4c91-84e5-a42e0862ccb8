🧠 Zoobbe Pro Workspace Billing Algorithm
1. Seat Management
When a new member is added to a Pro workspace:
A seat is automatically added.
The workspace admin is required to pay for the extra seat.
If payment is not completed:
The admin sees a payment notice.
The new member gets view-only access and sees a notice to inform the admin.
2. Billing Cycle
Billing is workspace-based, and follows the chosen cycle:
Either monthly or annually, based on the initial subscription.
The next billing cycle starts on the anniversary of the workspace subscription start date (month or year).
3. Seat Reuse Within the Same Cycle
If a member is removed during the current billing cycle, the seat remains reserved.
Admins can add a new member using the reserved seat without additional billing.
4. Prorated Charges
If a new seat is added in the middle of a billing cycle, Zoobbe will:
Prorate the charge based on the remaining time in the current cycle.
Apply this logic for both monthly and annual billing.
